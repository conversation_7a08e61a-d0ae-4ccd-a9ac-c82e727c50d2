{"name": "coreui-free-angular-admin-template", "version": "5.5.1", "copyright": "Copyright 2024 creativeL<PERSON><PERSON><PERSON>", "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people) and contributors", "homepage": "https://coreui.io/angular", "config": {"theme": "default", "coreui_library_short_version": "5.5", "coreui_library_docs_url": "https://coreui.io/angular/docs/"}, "scripts": {"ng": "ng", "start": "ng serve -o", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/language-service": "^18.2.13", "@angular/localize": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@coreui/angular": "^5.2.25", "@coreui/angular-chartjs": "^5.2.25", "@coreui/chartjs": "^4.0.0", "@coreui/coreui": "^5.2.0", "@coreui/icons": "^3.0.1", "@coreui/icons-angular": "^5.2.25", "@coreui/utils": "^2.0.2", "chart.js": "^4.4.6", "lodash-es": "^4.17.21", "ngx-scrollbar": "^13.0.3", "rxjs": "^7.8.2", "tslib": "^2.8.1", "zone.js": "^0.14.10"}, "devDependencies": {"@angular/cli": "^18.2.13", "@angular/compiler-cli": "^18.2.13", "@types/jasmine": "^5.1.8", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.32", "jasmine-core": "^5.8.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "^5.5.4"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": ">= 6"}, "bugs": {"url": "https://github.com/coreui/coreui-free-angular-admin-template/issues"}, "repository": {"type": "git", "url": "git+https://github.com/coreui/coreui-free-angular-admin-template.git"}}